<template>
    <a-spin :spinning="state.treeSpinning">
        <a-button
            style="margin-bottom: 10px"
            v-if="activeKey == 'custom'"
            type="primary"
            block
            @click="handerCustomAddEdit(null)"
        >
            <template #icon>
                <plus-outlined />
            </template>
            添加自定义分组
        </a-button>
        <DempTree
            style="min-height: 200px; transition: all 0.25s"
            ref="dempTree"
            :treeData="state.treeData"
            :selectedKeys="state.selectedKeys"
            :defaultExpand="state.defaultExpand"
            :fieldNames="state.fieldNames"
            @emitSelect="handleTreeSelect"
        >
            <!-- 自定义分组 -->
            <template
                #handleItem="{ handleItem }"
                v-if="['outsiders', 'custom'].includes(activeKey)"
            >
                <span v-if="activeKey == 'outsiders' && handleItem.numberCount">
                    （{{ handleItem.numberCount }}）
                </span>

                <a-button
                    v-if="activeKey == 'custom'"
                    class="outs-btn"
                    size="small"
                    :type="handleItem.id === state.activeId ? 'primary' : ''"
                >
                    {{ handleItem.type ? "外部" : "内部" }}
                </a-button>
                <a-dropdown v-if="activeKey == 'custom'" :trigger="['click']">
                    <a
                        class="ant-dropdown-link handle_icon"
                        @click.prevent.stop
                    >
                        <more-outlined />
                    </a>
                    <template #overlay>
                        <a-menu>
                            <div>
                                <a-menu-item
                                    @click.stop="
                                        handerCustomAddEdit(handleItem)
                                    "
                                >
                                    <a-button type="text"> 编辑分组 </a-button>
                                </a-menu-item>
                            </div>
                            <div>
                                <a-menu-item
                                    @click.stop="handerCustomRemove(handleItem)"
                                >
                                    <a-button type="link" danger>
                                        删除分组
                                    </a-button>
                                </a-menu-item>
                            </div>
                        </a-menu>
                    </template>
                </a-dropdown>
            </template>
        </DempTree>
    </a-spin>

    <AddEidtCustom
        v-model:visible="state.visible"
        @submit="getList(activeKey)"
        :groupId="state.groupId"
        :grougName="state.grougName"
    />
</template>

<script setup>
import AddEidtCustom from "../components/addEidtCustom.vue"
import { Modal, message } from "ant-design-vue"
import { ExclamationCircleFilled } from "@ant-design/icons-vue"

const setFaceDatabase = inject("setFaceDatabase")
const props = defineProps({
    activeKey: {
        type: String,
        default: "teacher"
    }
})

const dempTree = shallowRef()
const state = reactive({
    title: "宝安职业大学",
    treeSpinning: false,
    listSpinning: false,
    treeData: [],
    selectedKeys: [],
    defaultExpand: [],
    fieldNames: {
        children: "children",
        title: "name",
        key: "id"
    },
    deptList: [],
    rollList: [],
    outsidersList: [],
    customList: [],
    visible: false,
    groupId: "",
    grougName: ""
})

/**
 * @description: 切换部门
 * @param {id,item} 部门id（tree、id）- item 当前部门，tree节点{}
 */
const handleTreeSelect = (id, item) => {
    const { showName, name, type, children, rollValue } = item
    // state.activeId = id;
    state.outsiders = !!type
    state.title = showName || name
    // state.query.id = id;
    // state.query.rollType = type;
    // state.query.rollValue = rollValue;
    state.selectedKeys = [id]
    // state.query.pageNo = 1;
    // state.query.pageSize = 24;
    // state.query.total = 0;
    // 判断是否是老师组且是最底层
    // state.isTeacherDelpMinimum = !!(state.query.type == 2 && !children.length);
    setFaceDatabase.id = id
    setFaceDatabase.title = showName || name
    setFaceDatabase.rollType = type
}
// 新增编辑自定义分组
const handerCustomAddEdit = (item) => {
    if (item) {
        state.groupId = item.id
        state.grougName = dempTree.value.titleObj[item.id]
    } else {
        state.groupId = ""
        state.grougName = ""
    }
    state.visible = true
}
// 删除自定义分组
const handerCustomRemove = (item) => {
    Modal.confirm({
        title: "删除提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "删除后不可恢复，如需继续删除，请点击确定",
        okType: "danger",
        okText: "确定",
        cancelText: "取消",
        onOk() {
            http.post("/cloud/faceGroup/delete", { id: item.id }).then(
                (res) => {
                    message.success(res.message)
                    getList(props.activeKey)
                }
            )
        },
        onCancel() {
            console.log("Cancel")
        }
    })
}
const groupApi = {
    teacher: "/cloud/app/dept/list", // 获取部门
    student: "/cloud/app/roll/listTree", //获取学籍
    outsiders: "/cloud/externalGroup/externalGroupNumberList", //获取外部人员
    custom: "/cloud/faceGroup/list" // 获取自定义分组
}
// 获取组
const getList = async (item) => {
    http.get(groupApi[item]).then(({ data }) => {
        if (item === "outsiders") {
            state.treeData = [
                {
                    id: "-10000",
                    name: "外部人员",
                    children: data
                }
            ]
        } else {
            state.treeData = data
        }
        if (state.treeData.length) {
            const { id, name, showName } = state.treeData[0]
            state.selectedKeys = [id || ""]
            state.defaultExpand = [id || ""]
            setFaceDatabase.id = id
            setFaceDatabase.title = name || showName
        }
    })
}
watch(
    () => props.activeKey,
    (val) => {
        getList(val)
    },
    { immediate: true }
)
</script>

<style lang="less" scoped>
.outs-btn {
    position: absolute;
    right: 14px;
    top: 6px;
    transform: scale(0.7);
}
</style>
