<template>
    <div style="display: inline-block">
        <a-button style="margin-left: 12px" @click="open">
            <slot />
        </a-button>
        <a-modal
            v-model:open="state.visible"
            title="通知个人采集"
            @ok="handleOk"
            @cancel="cancel"
            width="700px"
        >
            <div style="padding: 20px">
                <div class="notice_title">
                    未采集人脸数量：
                    <span class="notice_num">{{ state.total }}条</span>
                </div>
                <a-form>
                    <a-form-item v-bind="validateInfos.content">
                        <a-textarea
                            v-model:value="modelRef.content"
                            show-count
                            :maxlength="200"
                            style="width: 100%"
                            :rows="5"
                        />
                    </a-form-item>
                </a-form>
                <div class="notice_hint">
                    <exclamation-circle-filled class="notice_hint__icon" />
                    <span
                        >发送通知后将提醒{{
                            state.total
                        }}个教职工或家长自行采集人脸信息</span
                    >
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
import { reactive, watch, toRaw } from "vue"
import { Form, message } from "ant-design-vue"

const props = defineProps({
    visible: Boolean,
    id: String,
    rollType: String
})

const useForm = Form.useForm
const emit = defineEmits(["ok", "update:visible"])

const modelRef = reactive({
    content: ""
})
const state = reactive({
    spinning: false,
    total: 0,
    visible: false
})
const rule = reactive({
    content: [
        {
            required: true,
            message: "请输入"
        },
        { min: 0, max: 200, message: "最多只能输入200 个字符" }
    ]
})

const { resetFields, validate, validateInfos } = useForm(modelRef, rule)
watch(
    () => {
        return props.visible
    },
    (v) => {
        state.visible = v
        emit("update:visible", v)
    }
)

watch(
    () => {
        return state.visible
    },
    (v) => {
        emit("update:visible", v)
    }
)

const open = () => {
    state.visible = true
    emit("update:visible", true)
    http.post("/cloud/face/number", {
        id: props.id,
        rollType: props.rollType
    }).then((res) => {
        const { content, number } = res.data
        state.total = number || 0
        modelRef.content = content
    })
}
const cancel = () => {
    state.visible = true
    emit("update:visible", false)
}

const handleOk = () => {
    if (state.total == 0) {
        message.warning("该部门/班级没有采集人员")
        return
    }

    validate().then(() => {
        emit("ok", toRaw(modelRef))
    })
}
</script>

<style scoped lang="less">
.notice_title {
    margin-bottom: 28px;
}

.notice_num {
    color: var(--error-color);
}

.notice_hint {
    .notice_hint__icon {
        color: #faad14;
        padding-right: 8px;
    }
}
</style>
