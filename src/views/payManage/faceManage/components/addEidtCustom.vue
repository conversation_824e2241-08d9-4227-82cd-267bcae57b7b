<template>
    <!-- 自定义分组添加、修改 -->
    <a-modal
        v-model:open="props.visible"
        width="430px"
        :title="props.groupId ? '编辑分组' : '添加分组'"
        :okButtonProps="{
            loading: state.customGloupLoading
        }"
        @ok="handerCustom"
        @cancel="handlrCancel"
        destroyOnClose
    >
        <a-form
            ref="customRef"
            layout="vertical"
            :model="state.form"
            :rules="rules"
        >
            <a-form-item label="分组名称：" name="name">
                <a-input
                    v-model:value="state.form.name"
                    placeholder="请输入"
                    :maxlength="20"
                />
            </a-form-item>
            <!-- 大学版 -->
            <template v-if="!props.groupId">
                <a-form-item label="分组类型：" name="type">
                    <a-select
                        v-model:value="state.form.type"
                        :options="groupOptions"
                        placeholder="请选择"
                    />
                </a-form-item>
            </template>
        </a-form>
    </a-modal>
</template>

<script setup>
const groupOptions = [
    {
        value: 0,
        label: "本校师生"
    },
    {
        value: 1,
        label: "非本校师生"
    }
]

const rules = {
    name: [{ required: true, message: "请输入模板名称", trigger: "blur" }],
    type: [{ required: true, message: "请选择模板类型", trigger: "change" }]
}
const customRef = ref(null)
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    groupId: {
        type: String,
        default: ""
    },
    grougName: {
        type: String,
        default: ""
    }
})
const emit = defineEmits(["update:visible", "submit"])
const state = reactive({
    customGloupLoading: false,
    customGloupIsEdit: "",
    form: {
        id: "",
        name: "",
        type: null
    }
})
const handlrCancel = () => {
    emit("update:visible", false)
}
// 自定义分组
const handerCustom = () => {
    customRef.value.validate().then(() => {
        let API = "/cloud/faceGroup/create"
        if (props.groupId) {
            API = "/cloud/faceGroup/update"
        }
        http.post(API, state.form).then((res) => {
            handlrCancel()
            emit("submit")
        })
    })
}
watch(
    () => props.visible,
    (val) => {
        if (val) {
            state.form.id = props.groupId
            state.form.name = props.grougName
        }
    }
)
function initPage() {}

onMounted(() => {
    initPage()
})
</script>

<style lang="less" scoped>
.container {
}
</style>
