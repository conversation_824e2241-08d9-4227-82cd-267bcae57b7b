<template>
    <div style="display: inline-block">
        <a-button @click="open" :disabled="props.disableds">
            <template #icon>
                <plus-outlined v-if="buttonType" />
            </template>
            <slot />
        </a-button>
        <a-modal
            v-model:open="props.visible"
            :title="multiple ? '批量采集' : '个人采集'"
            @ok="handleOk"
            :okButtonProps="{
                loading: state.loading,
                disabled: uploadDisabled
            }"
            @cancel="handleCancel"
            width="740px"
            :okText="state.okText"
            :destroyOnClose="true"
        >
            <div style="padding: 20px">
                <div class="upload_wap">
                    <a-spin
                        :tip="state.progress"
                        :indicator="indicator"
                        :spinning="state.spinning"
                    >
                        <div class="upload">
                            <a-upload
                                :before-upload="beforeUpload"
                                :showUploadList="false"
                                :fileList="state.fileList"
                                @change="handleChange"
                                name="file"
                                :multiple="multiple"
                                action="/"
                                class="upload_comp"
                                accept="image/png,image/jpg,image/jpeg"
                            >
                                <a-button
                                    type="primary"
                                    ghost
                                    style="margin-top: 75px"
                                    :disabled="
                                        !multiple &&
                                        state.imageList.length > imgNum
                                    "
                                >
                                    选择照片
                                </a-button>
                            </a-upload>
                            <p class="upload_format__hint upload_hint__rule">
                                仅支持上传jpeg/jpg/png格式文件，单个文件不能超过1M,单次最多可选{{
                                    imgNum
                                }}张
                            </p>
                        </div>
                        <!-- 已上传图片 -->
                        <div class="sorel" v-bind="containerProps">
                            <div v-bind="wrapperProps">
                                <a-row :gutter="[24, 24]" class="upload_list">
                                    <a-col
                                        :span="4"
                                        v-for="(item, index) in list"
                                        :key="item.data.id + 'image' + index"
                                    >
                                        <div class="upload_item__warp">
                                            <div
                                                class="upload_item_image__warp"
                                            >
                                                <a-image
                                                    class="upload_item_image"
                                                    :preview="false"
                                                    width="86px"
                                                    height="86px"
                                                    :src="item.data.url"
                                                ></a-image>
                                                <close-circle-filled
                                                    class="upload_item__remove"
                                                    title="删除"
                                                    @click="removeUpload(index)"
                                                />
                                                <div v-if="item.data.errorMsg">
                                                    <div
                                                        class="upload_item__error__warp"
                                                    ></div>
                                                    <a-tooltip placement="top">
                                                        <template #title>{{
                                                            item.data.errorMsg
                                                        }}</template>
                                                        <exclamation-circle-filled
                                                            class="upload_item__error"
                                                        />
                                                    </a-tooltip>
                                                </div>
                                            </div>
                                            <div class="upload_file__name">
                                                <span
                                                    title="单击可以编辑名字"
                                                    >{{ item.data.name }}</span
                                                >
                                            </div>
                                        </div>
                                    </a-col>
                                </a-row>
                            </div>
                        </div>
                    </a-spin>
                </div>
                <div class="upload_hint">
                    <div class="upload_hint__title">
                        <exclamation-circle-filled class="upload_hint__icon" />
                        <span>照片命名规则：</span>
                    </div>
                    <div class="upload_hint__rule">
                        教职工和学生的照片文件命名为“姓名”，如 "张三"
                        {{
                            !outsiders
                                ? "，如存在重名情况，则教职工重名命名为“姓名-手机号”，学生重名命名为“姓名-学籍号”"
                                : ""
                        }}
                    </div>
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
import { reactive, h, watch } from "vue"
import { LoadingOutlined } from "@ant-design/icons-vue"
import { message } from "ant-design-vue"
import { useRoute } from "vue-router"
import { useVirtualList } from "@vueuse/core"

const route = useRoute()
const props = defineProps({
    visible: Boolean,

    id: String,
    type: [String, Number],
    userId: String,
    title: String,
    multiple: {
        type: Boolean,
        default: true
    },
    buttonType: {
        type: String,
        default: ""
    },
    outsiders: {
        type: Boolean,
        default: false
    },
    disableds: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(["update:visible", "changeMultiple", "complete"])

const indicator = h(LoadingOutlined, {
    style: {
        fontSize: "24px"
    },
    spin: true
})

const state = reactive({
    fileList: [],
    spinning: false,
    loading: false,
    progress: `0%`,
    okText: "上传",
    visible: false,
    image1M: false, // 图片是否大于1M
    updateLogin: false,
    imageList: []
})

// let imageList = ref([]);
let imgNum = ref(1)

let { list, containerProps, wrapperProps } = useVirtualList(state.imageList, {
    // 确保 `itemHeight` 与每行的高度保持同步。
    itemHeight: 18
})

const handleCancel = () => {
    imgNum.value = 1
    state.imageList.length && emit("complete")
    // state.imageList = []
    list.value.length = 0
    emit("update:visible", false)
}
const beforeUpload = async (file) => {
    const { size, name, uid } = file
    const isLt1M = size / 1024 / 1024 < 1
    state.updateLogin = false
    if (!isLt1M) {
        message.warning("上传图像须小于1MB！")
    } else {
        // 有id 则只能上传一张
        if (props.userId) {
            state.imageList.length = 0
            state.fileList.length = 0
        }
        const _item = {
            name,
            file,
            url: await getBase64(file),
            uid,
            path: null,
            errorMsg: ""
        }
        if (state.imageList.length < imgNum.value) {
            state.imageList.push(_item)
            state.fileList.push(file)
        }
    }
    state.spinning = true
    state.progress = "正在导入中..."

    return false
}
const handleChange = () => {
    state.updateLogin = true
    state.spinning = false
}
const uploadDisabled = computed(() => {
    if (state.updateLogin && state.imageList.length) {
        return false
    }
    return true
})

// 新 轮循提交
const handleOk = async () => {
    if (state.fileList.length > imgNum.value) {
        return message.warning(`超出限制，最多只能选择${imgNum.value}张照片！`)
    }
    const imageLeng = state.imageList.length
    //  JSON.parse(JSON.stringify(state.imageList))
    state.spinning = true
    state.loading = true
    state.progress = "0%"
    const newImageList = []
    const errorLen = []
    for (let i = 0; i < imageLeng; i++) {
        const item = state.imageList[i]
        // const formData = new FormData()
        const { id, type, outsiders, userId } = props
        // formData.append("file", item.file)
        // formData.append("type", outsiders ? 3 : type)
        // userId && formData.append("userId", userId)
        // // formData.append('id', id)
        // // 目前只有在学生组 并且非班级的情况下上传 去掉id(后端没处理 等后端处理了下面则删除)
        // if (route.path == "/basicManagement/faceLibrary/student") {
        //     route.query.rollValue == "classes" && formData.append("id", id)
        // } else {
        //     formData.append("id", id)
        // }

        http.postForm("/cloud/face/createFace", {
            file: item.file,
            type: outsiders ? 3 : type,
            userId,
            id
        })
            .then((res) => {
                const msgList = res.data
                if (msgList.length) {
                    state.imageList.forEach((cur, index) => {
                        // item.status 失敗
                        const obj = msgList.find(
                            (item) => item.fileName === cur.name
                        )
                        if (obj && obj.status) {
                            cur.errorMsg = obj.errorInfo
                            if (obj.errorInfo) {
                                errorLen.push(obj.errorInfo)
                            }
                        }
                    })
                    state.progress = `${Math.floor((i / imageLeng) * 100)}%`
                    if (imageLeng - 1 === i) {
                        state.progress = `100%`
                        state.spinning = false
                        state.loading = false
                    }
                    // 木有错误则关弹框
                    if (!errorLen.length && imageLeng - 1 === i) {
                        setTimeout(() => {
                            handleCancel()
                        }, 1000)
                    }
                }
            })
            .catch(() => {
                // state.spinning = false;
            })
    }
    if (newImageList.length) {
        state.fileList = newImageList
        state.imageList = newImageList
    }
}
const open = () => {
    imgNum.value = 5000
    emit("changeMultiple")
    emit("update:visible", true)
}

const getBase64 = (file) => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => resolve(reader.result)
        reader.onerror = (error) => reject(error)
    })
}

const removeUpload = (index) => {
    state.imageList.splice(index, 1)
    state.fileList.splice(index, 1)
}
watch(
    () => props.visible,
    (val) => {
        state.fileList = []
        state.imageList.length = 0
        state.spinning = false
        state.loading = false
        // 单个上传
        if (val && props.userId) {
            imgNum.value = 1
        }
    }
)
</script>

<style scoped lang="less">
.upload_wap {
    min-height: 218px;
    padding: 0 16px 4px 16px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    box-sizing: content-box;
    overflow: hidden;

    .upload {
        text-align: center;
    }

    transition: all 0.25s;
}

.upload_list {
    padding-top: 8px;
    max-height: 290px;
    overflow-y: scroll;
}

.upload_format__hint {
    margin-top: 16px;
}

.upload_hint {
    &__title {
        color: rgba(0, 0, 0, 0.85);
        margin-top: 24px;
        margin-bottom: 8px;
    }

    &__icon {
        color: #faad14;
        padding-right: 8px;
    }

    &__rule {
        color: rgba(0, 0, 0, 0.45);
        line-height: 22px;
    }
}

.sorel {
    height: 235px;
    overflow: hidden;

    .upload_item__warp {
        &:hover {
            .upload_item__remove {
                display: block;
            }

            .upload_file__name {
                span {
                    color: var(--primary-color);
                }
            }
        }

        .upload_file__name {
            padding: 8px 0;
            text-align: center;
            color: rgba(0, 0, 0, 0.45);
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .upload_item_image__warp {
        position: relative;
        width: 86px;
        height: 86px;

        .upload_item__remove {
            display: none;
            position: absolute;
            right: -7px;
            top: 0px;
            color: var(--error-color);
            font-size: 14px;
            background: #fff;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.25s;
            z-index: 1;
        }

        .upload_item__error__warp {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background: #fff;
            opacity: 0.3;
        }

        .upload_item__error {
            position: absolute;
            font-size: 14px;
            left: 50%;
            top: 50%;
            transform: translate3d(-25%, -25%, 0);
            color: #faad14;
            background: #fff;
            border-radius: 50%;
            cursor: pointer;
        }
    }
}
</style>
