<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-02-22 11:40:50
 * @LastEditors: jingrou
 * @LastEditTime: 2022-11-14 16:50:52
-->
<template>
    <a-row type="flex">
        <a-col style="margin-bottom: 16px">
            <a-radio-group
                v-model:value="status"
                button-style="solid"
                @change="change"
            >
                <a-radio-button
                    :value="item.value"
                    v-for="(item, index) in data"
                    :key="`tab_${index}`"
                >
                    {{ item.label }}（<span
                        :style="{
                            color: index == data.length - 1 && '#f8535c'
                        }"
                    >
                        {{ item.count }}</span
                    >）
                </a-radio-button>
            </a-radio-group>
        </a-col>
        <a-col style="margin-left: 12px">
            <a-form layout="inline">
                <a-form-item>
                    <a-input
                        v-model:value="name"
                        @change="inputChange"
                        :placeholder="placeholder"
                        max="100"
                    ></a-input>
                </a-form-item>
                <a-form-item>
                    <a-button type="primary" @click="handleSearch"
                        >查 询</a-button
                    >
                </a-form-item>
                <a-form-item>
                    <a-button @click="$emit('reset')">重 置</a-button>
                </a-form-item>
            </a-form>
        </a-col>
    </a-row>
</template>
<script setup>
const props = defineProps({
    placeholder: String,
    status: {
        type: Number,
        default: 1
    },
    name: String,
    data: {
        type: Array,
        BVdefault: () => []
    }
})

const placeholder = computed(() => {
    return props.placeholder
})
const name = computed(() => {
    return props.name
})
const status = computed(() => {
    return props.status
})
const data = computed(() => {
    return props.data
})

const emit = defineEmits(["search", "update:name", "update:status"])
const change = (e) => {
    emit("update:status", e.target.value)
    emit("search", {
        name: props.name,
        status: e.target.value
    })
}
const inputChange = (e) => {
    emit("update:name", e.target.value)
}
const handleSearch = () => {
    emit("search", {
        name: props.name,
        status: props.status
    })
}
</script>
<style scoped lang="less"></style>
