<!--
 * @Descripttion: 人脸列表
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-02-22 11:23:18
 * @LastEditors: 舒志建 <EMAIL>
 * @LastEditTime: 2022-11-02 15:28:17
-->
<template>
    <div>
        <a-checkbox-group
            v-model:value="state.checkboxGroup"
            style="width: 100%"
            class="personnel_list__warp"
            @change="hanlechange"
        >
            <a-row :gutter="[16, 16]">
                <a-col
                    :md="3"
                    :lg="6"
                    :xl="4"
                    :xxl="3"
                    class="personnel_item__warp"
                    v-for="(item, index) in data"
                    :key="index"
                >
                    <!--  -->
                    <div class="personnel_item">
                        <!-- 已上传 -->
                        <div
                            class="personnel_item__upload__warp"
                            @click="openCollection(item, index)"
                        >
                            <div
                                :style="{
                                    background: `url(${
                                        item.imgPath
                                            ? item.imgPath
                                            : '/src/assets/images/defal_user.png'
                                    })`,
                                    filter: `blur(20px)`,
                                    width: `100%`,
                                    height: `100%`,
                                    position: `absolute`,
                                    backgroundSize: `auto`,
                                    overflow: `hidden`
                                }"
                            ></div>
                            <a-image
                                v-if="item.collect == 0"
                                class="image"
                                :preview="false"
                                :width="`100%`"
                                urlBg(item.imgPath)
                                :src="`${
                                    item.imgPath
                                        ? item.imgPath
                                        : '/src/assets/images/defal_user.png'
                                }`"
                            ></a-image>
                            <div v-else class="personnel_item__upload">
                                <img
                                    v-if="mode == 'multiple'"
                                    src="/src/assets/images/defal_user.png"
                                    alt
                                    style="width: 100%; height: 100%"
                                />

                                <plus-outlined
                                    v-else
                                    class="personnel_item__upload_icon"
                                />
                            </div>
                            <span
                                v-if="item.imgPath && mode == 'multiple'"
                                class="status_label"
                                :class="{ active: item.status }"
                            >
                                {{ item.status ? "失效" : "生效" }}
                            </span>
                        </div>

                        <div
                            style="
                                padding: 12px 0 12px 0;
                                text-align: center;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            "
                        >
                            <a-checkbox
                                :value="item.id"
                                v-if="mode == 'multiple'"
                                :title="item.name"
                                >{{ item.name }}</a-checkbox
                            >
                            <span v-else :title="item.name">
                                {{ item.name }}
                            </span>
                        </div>
                    </div>
                </a-col>
            </a-row>
        </a-checkbox-group>

        <a-empty
            v-if="!data.length"
            image="/src/assets/images/faceEmpty.png"
            :image-style="{ height: '180px' }"
            description="暂无搜索匹配结果"
        ></a-empty>
    </div>
</template>
<script setup>
import { useRoute } from "vue-router"

const route = useRoute()
const props = defineProps({
    mode: {
        type: String,
        default: "multiple"
    },
    data: {
        type: Array,
        default: () => []
    },
    selectUserIds: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(["open", "changeSelect"])

const state = reactive({
    checkboxGroup: []
})

watch(
    () => {
        return props.selectUserIds
    },
    (v) => {
        state.checkboxGroup = v
    }
)

const openCollection = (item) => {
    // const btnList = route.meta?.btnList || []
    // const btnListValue = btnList.map((i) => i.value)
    // console.log(btnListValue, "btnListValue", route.meta)

    // const isOpen =
    //     btnListValue.includes("batchCollection") ||
    //     btnListValue.includes("faceLibraryOutsiders")
    //  isOpen &&
    emit("open", item)
}
const hanlechange = (data) => {
    emit("changeSelect", data)
}
</script>
<style scoped lang="less">
.personnel_item__upload__warp {
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    align-items: center;

    .status_label {
        display: block;
        background: var(--primary-color);
        color: var(--bg-color);
        border-radius: 25px;
        padding: 0 12px;
        font-size: 12px;
        position: absolute;
        top: 0;
        right: -7px;

        &.active {
            background: #b8b8b8;
        }
    }
}

.personnel_item__warp {
    .personnel_item {
        padding: 12px 12px 0 12px;
        box-sizing: border-box;
        background: var(--bg-color);
        border-radius: 4px;
        height: 100%;

        display: flex;
        flex-direction: column;
        // justify-content:flex-end;
    }

    .personnel_item__upload {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        min-height: 128px;
        height: 100%;
        background: var(--bg-color);
        box-sizing: border-box;
        border: 1px dashed #d8d8d8;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.25s;

        &:hover {
            border-color: var(--primary-color);
        }

        &_icon {
            font-size: 24px;
            color: var(--text-color);
        }
    }
}
</style>

<style lang="less">
.personnel_item__warp {
    .ant-image {
        height: 100%;
        height: 128px;
        overflow: hidden;
    }

    .image {
        border-radius: 4px;
        min-height: 128px;
        object-fit: contain;
        height: 100%;
    }
}
</style>
