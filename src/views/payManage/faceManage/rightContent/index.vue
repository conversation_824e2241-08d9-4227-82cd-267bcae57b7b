<template>
    <div class="faceLibrary_type">
        <div class="faceLibrary_type__title">{{ setFaceDatabase.title }}</div>
        <a-spin :spinning="state.listSpinning">
            <div class="faceLibrary_type__search">
                <FaceSearch
                    v-model:status="state.form.status"
                    v-model:name="state.form.name"
                    :placeholder="state.placeholder"
                    @search="handleSearch"
                    @reset="handleReset"
                    :data="statusList"
                />

                <a-space v-if="setFaceDatabase.type == 3">
                    <a-button type="primary">
                        <template #icon>
                            <plus-outlined />
                        </template>
                        添加成员
                    </a-button>
                    <a-button
                        :disabled="!state.selectUserIds.length"
                        @click="handerCustomDelete"
                        danger
                        >批量删除</a-button
                    >
                </a-space>

                <a-space v-if="[1, 2, 4].includes(setFaceDatabase.type)">
                    <BatchCollect
                        title="批量采集"
                        :multiple="state.multipleUpload"
                        :id="state.form.id"
                        :type="state.form.type"
                        :userId="state.singleCollect.userId"
                        v-model:visible="state.batchCollectVisible"
                        @changeMultiple="handleChangeMultiple"
                        @complete="handleComplete"
                        >批量采集</BatchCollect
                    >

                    <NoticeCollect
                        v-if="setFaceDatabase.type !== 4"
                        :id="state.form.id"
                        :rollType="
                            state.form.type == 1 ? state.form.rollType : ''
                        "
                        v-model:visible="state.noticeCollectVisible"
                        @ok="handleNotice"
                    >
                        通知个人采集
                    </NoticeCollect>
                </a-space>
            </div>
            <FaceList
                :mode="setFaceDatabase.type == 3 ? 'multiple' : 'none'"
                :data="state.facePageList"
                @open="handleCollect"
                @changeSelect="changeSelect"
                style="min-height: 500px"
                :selectUserIds="state.selectUserIds"
            />

            <a-pagination
                class="paginations"
                v-model:current="state.paginations.pageNo"
                v-model:pageSize="state.paginations.pageSize"
                :total="state.paginations.total"
                show-quick-jumper
                show-less-items
                @change="handlePagination"
                :show-total="(total) => `共 ${total} 条`"
                :pageSizeOptions="['10', '20', '24', '48', '50', '60', '80']"
            />
        </a-spin>
        <BatchCollect
            title="个人采集"
            :multiple="false"
            :id="state.form.id"
            :type="state.form.type"
            :userId="state.singleCollect.userId"
            v-model:visible="state.batchPersonVisible"
            @complete="handleComplete"
        />
    </div>
</template>

<script setup>
import { reactive, computed, inject, createVNode } from "vue"
import { Modal, message } from "ant-design-vue"
import { ExclamationCircleFilled } from "@ant-design/icons-vue"
import FaceList from "../components/faceList.vue"
import FaceSearch from "../components/faceSearch.vue"
import BatchCollect from "../components/batchCollect.vue"
import NoticeCollect from "../components/noticeCollect.vue"

const statusList = ref([
    { label: "全部", value: 1, count: computed(() => state.countAll) },
    { label: "已采集", value: 2, count: computed(() => state.count) },
    { label: "未采集", value: 3, count: computed(() => state.countNot) }
])
const setFaceDatabase = inject("setFaceDatabase")
const state = reactive({
    title: "宝安职业大学",
    listSpinning: false,
    facePageList: [],
    selectUserIds: [],
    placeholder: "请输入学生姓名搜索",
    countAll: 0, // 采集总数
    countNot: 0, // 未采集
    count: 0, // 已采集
    form: {
        id: "",
        type: 1,
        status: 1, // 全部:1已采集:2 未采集:3,
        rollType: null, // 类型 :0学校 1 校区 2 学段 3 年级 4 班级 老师和自定义不用填
        name: ""
    },
    paginations: {
        pageNo: 1,
        pageSize: 24,
        total: 0
    },
    // 批量采集
    batchCollectVisible: false,
    multipleUpload: true,
    type: "",
    singleCollect: {
        userId: ""
    },
    // 个人采集
    batchPersonVisible: false,
    // 个人通知
    noticeCollectVisible: false
})
const handleNotice = (item) => {
    const { id, type, rollType } = setFaceDatabase
    http.post("/cloud/face/send", {
        id,
        rollType,
        type,
        content
    })
        .then((res) => {
            message.success(res.message)
            state.noticeCollectVisible = false
        })
        .catch((err) => {
            message.error(err.message)
        })
}
// 批量采集
const handleChangeMultiple = () => {
    state.multipleUpload = true
    state.singleCollect.userId = ""
}
// 采集完成
const handleComplete = () => {
    initPage()
}
const changeMultiple = () => {
    state.multipleUpload = true
    state.singleCollect.userId = ""
}

// 自定义批量删除
const handerCustomDelete = () => {
    Modal.confirm({
        title: "删除提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "删除后不可恢复，如需继续删除，请点击确定",
        okType: "danger",
        okText: "确定",
        cancelText: "取消",
        onOk() {
            const { id, type, rollType } = setFaceDatabase
            const param = {
                userIds: state.selectUserIds,
                groupId: id,
                rollType,
                type
            }
            http.post("/cloud/face/deleteCustomPeople", param).then((res) => {
                message.success(res.message)
                initPage()
            })
        },
        onCancel() {
            console.log("Cancel")
        }
    })
}
// 个人采集
const handleCollect = (item) => {
    state.singleCollect.userId = item.id
    state.batchCollectVisible = false
    state.batchPersonVisible = true
}
// 选中人脸诈骗片
const changeSelect = (item) => {
    state.selectUserIds = item
}

const handlePagination = (page, pageSize) => {
    state.paginations.pageNo = page
    initPage()
}
const initPage = () => {
    state.listSpinning = true
    const params = { ...state.form, ...state.paginations }
    http.post("/cloud/face/facePage", params)
        .then(({ data }) => {
            const { page, countAll, count } = data
            state.facePageList = page.list
            state.paginations.pageNo = page.pageNo || 1
            state.paginations.pageSize = page.pageSize || 24
            state.paginations.total = page.total || 0
            // 采集数据
            state.countAll = countAll || 0
            state.count = count || 0
            state.countNot = countAll - count || 0
        })
        .finally(() => {
            state.listSpinning = false
        })
}
// 重置
const handleReset = () => {
    state.paginations.pageNo = 1
    state.form.name = ""
    initPage()
}
// 查询
const handleSearch = (item) => {
    state.form.name = item.name
    state.form.status = item.status
    state.paginations.pageNo = 1
    initPage()
}
watch(
    () => setFaceDatabase.id,
    (val) => {
        if (val) {
            state.form.id = val
            state.form.type = setFaceDatabase.type
            state.facePageList = []
            state.form.name = ""
            state.form.status = 1
            state.paginations.pageNo = 1
            state.paginations.pageSize = 24
            setTimeout(() => {
                initPage()
            }, 100)
        }
    },
    { immediate: true, deep: true }
)
</script>

<style scoped lang="less">
.faceLibrary_type {
    padding: 0 16px;

    .faceLibrary_type__title {
        padding: 24px 0;
        font-size: 18px;
        font-weight: 500;
        color: var(--text-color);
    }

    .faceLibrary_type__search {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .paginations {
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;
    }
}
</style>
