<template>
    <div class="equipment_mgt">
        <a-tabs v-model:activeKey="state.activeKey">
            <a-tab-pane
                v-for="item in tabPanes"
                :key="item.key"
                :tab="item.tab"
            ></a-tab-pane>
        </a-tabs>
        <a-row type="flex" class="faceLibrary_warp_pageitem">
            <a-col class="demp_tree">
                <LeftDemp :activeKey="state.activeKey" />
            </a-col>
            <a-col style="flex: 1">
                <rightContent></rightContent>
            </a-col>
        </a-row>
    </div>
</template>

<script setup>
import { provide } from "vue"
import LeftDemp from "./leftDemp/index.vue"
import rightContent from "./rightContent/index.vue"

const tabPanes = [
    { key: "teacher", tab: "老师组", type: 2 },
    { key: "student", tab: "学生组", type: 1 },
    { key: "outsiders", tab: "外部人员", type: 4 },
    { key: "custom", tab: "自定义", type: 3 }
]
const state = reactive({
    activeKey: "teacher",
    params: {
        title: "",
        id: "",
        type: 2,
        rollType: null // 类型 :0学校 1 校区 2 学段 3 年级 4 班级 老师和自定义不用填
    }
})
watch(
    () => state.activeKey,
    (val) => {
        state.params.type = tabPanes.find((item) => item.key === val).type
        state.params.rollType = 0
    }
)
provide("setFaceDatabase", state.params)
</script>
<style lang="less" scoped>
.equipment_mgt {
    min-height: calc(100vh - 120px);
    :deep(.ant-tabs-nav) {
        margin: 0;
        padding-left: 12px;
    }

    .demp_tree {
        width: 276px;
        padding: 16px;
        border-right: 1px solid #d9d9d9;
        overflow: hidden auto;
        box-sizing: border-box;
    }

    .faceLibrary_warp_pageitem {
        min-height: calc(100vh - 166px);

        .outs-btn {
            position: absolute;
            right: 14px;
            top: 6px;
            transform: scale(0.7);
        }

        :deep(.ant-tree-title) {
            margin-right: 44px;
        }
    }
}
</style>
