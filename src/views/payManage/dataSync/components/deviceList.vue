<template>
    <div class="device_list">
        <div class="operate">
            <!-- 标题  -->
            <div class="device_title">
                <span class="title">设备列表</span>
                <span class="quantity">共{{ list.length || 0 }}台</span>
            </div>

            <!-- 说明 -->
            <div class="tip">说明：只能同步数据在线的设备</div>

            <!-- 搜索 -->
            <div class="search">
                <a-input
                    v-model:value="searchData"
                    placeholder="请输入设备名称"
                    @change="getDeviceList"
                >
                    <template #suffix>
                        <SearchOutlined />
                    </template>
                </a-input>
            </div>

            <!-- 设备类型 -->
            <div class="device_type">
                <span>设备类型：</span>
                <a-select
                    v-model:value="deviceType"
                    style="width: 100%; flex: 1"
                    :options="deviceTypeList"
                    :allowClear="true"
                    placeholder="请选择"
                    :fieldNames="{ label: 'name', value: 'type' }"
                    @change="getDeviceList"
                >
                </a-select>
            </div>

            <!-- 按钮 -->
            <div class="operate_btn">
                <a-button
                    type="primary"
                    :disabled="!selectKeyDevice.length"
                    @click="batchBindDevice"
                    >批量关联</a-button
                >
                <a-button
                    type="primary"
                    :disabled="!selectKeyDevice.length"
                    @click="batchSyncDevice"
                    >批量同步</a-button
                >
            </div>
        </div>

        <!-- 设备列表 -->
        <div class="list">
            <a-checkbox-group v-model:value="selectKeyDevice">
                <a-row :gutter="[24]">
                    <a-col :span="24" v-for="item in list" :key="item.id">
                        <div
                            :class="{
                                active: selectDeviceId === item.id
                            }"
                            class="device_list_item"
                        >
                            <a-checkbox :value="item.id"></a-checkbox>
                            <div
                                class="device_item"
                                @click="selectDeviceFn(item)"
                            >
                                <div class="title_box">
                                    <div class="title">
                                        <div
                                            class="status"
                                            :class="{
                                                be_offline:
                                                    item.onlineStatus === 0
                                            }"
                                        ></div>
                                        {{ item.name || "-" }}
                                    </div>
                                    <span
                                        >{{ item.syncNumber || 0
                                        }}<span style="color: #b7b7b7"
                                            >/{{ item.bindNumber || 0 }}</span
                                        >
                                    </span>
                                </div>
                                <span class="number"
                                    >IMEI：{{ item.imei || "-" }}</span
                                >
                            </div>
                        </div>
                    </a-col>
                </a-row>
            </a-checkbox-group>
        </div>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"

const emit = defineEmits(["select", "batchBind", "batchSync"])
const searchData = ref("")
const deviceType = ref(null)
const deviceTypeList = ref([])
const selectKeyDevice = ref([]) // 多选
const selectDeviceId = ref(null) // 单选选中的设备
const list = ref([])

function selectDeviceFn(item) {
    selectDeviceId.value = item.id
}

// 获取设备类型
function getDeviceType() {
    http.get("/unicard/mgmt/device-info/device-type").then((res) => {
        deviceTypeList.value = res.data || []
    })
}

async function getDeviceList() {
    await http
        .post("/unicard/mgmt/device-sync/device-list", {
            text: searchData.value,
            deviceType: deviceType.value
        })
        .then((res) => {
            if (res.data.length) {
                list.value = res.data || []
                selectDeviceId.value = list.value[0]?.id
            } else {
                list.value = []
                selectDeviceId.value = null
            }
        })
}

watch(
    () => selectDeviceId.value,
    (val) => {
        emit("select", val)
    }
)

function batchBindDevice() {
    emit("batchBind", selectKeyDevice.value)
}

function batchSyncDevice() {
    const arr = list.value.filter((i) => selectKeyDevice.value.includes(i.id))
    const isOnline = arr.some((i) => i.onlineStatus == 0)
    // 过滤查找是否有离线的设备 如果有离线的设备不能给同步
    if (isOnline) {
        message.warning("只能同步数据在线的设备!")
        return
    }
    emit("batchSync", selectKeyDevice.value)
}

onMounted(async () => {
    getDeviceType()
    await getDeviceList()
})

function resetSelect() {
    selectKeyDevice.value = []
}

defineExpose({ resetSelect })
</script>

<style lang="less" scoped>
.device_list {
    padding: 16px 0px;

    .operate {
        padding: 0px 12px;
        margin-bottom: 12px;

        .device_title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 500;
            font-size: 14px;
            color: var(--text-color);
            line-height: 20px;
        }

        .tip {
            width: 100%;
            height: 22px;
            margin: 12px 0;
            background: #ffecd6;
            border-radius: 4px;
            font-weight: 400;
            font-size: 12px;
            color: var(--warning-color);
            line-height: 22px;
            padding: 0px 8px;
        }

        .search {
            margin-bottom: 16px;
        }

        .device_type {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .operate_btn {
            display: flex;
            align-items: center;

            .ant-btn {
                flex: 1;
            }
        }
    }

    .list {
        max-height: calc(100vh - 346px);
        padding: 0px 8px;
        overflow-y: auto;
        overflow-x: hidden;

        :deep(.ant-checkbox + span) {
            padding-right: 0 !important;
            width: 100%;
        }

        :deep(.ant-checkbox) {
            align-self: flex-start;
            margin-top: 6px;
        }

        .device_list_item {
            display: flex;
            width: 100%;
            max-width: 228px;
            padding: 12px 4px;
            border-bottom: 1px solid #d8d8d8;
        }

        .device_item {
            display: flex;
            flex-direction: column;
            flex: 1;
            margin-left: 10px;

            .title_box {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;

                .title {
                    display: flex;
                    align-items: center;
                    font-weight: 400;
                    font-size: 14px;
                    color: var(--text-color);
                    line-height: 20px;

                    .status {
                        width: 6px;
                        height: 6px;
                        background: var(--primary-color);
                        border-radius: 50%;
                        margin-right: 4px;
                    }

                    .be_offline {
                        background: #d8d8d8;
                    }
                }
            }

            .number {
                font-weight: 400;
                font-size: 12px;
                color: #999999;
                line-height: 17px;
                display: inline-block;
                white-space: nowrap;
                max-width: 192px;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }

    .active {
        background: var(--primary-color-bg) !important;
    }
}
</style>
