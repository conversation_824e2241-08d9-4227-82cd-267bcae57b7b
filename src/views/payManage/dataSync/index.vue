<template>
    <div class="data_sync">
        <div class="left_device">
            <device-list
                @select="selectReset"
                @batchBind="batchSingleBind"
                ref="deviceListRef"
                @batchSync="synchronize"
            />
        </div>
        <div class="user_content">
            <div class="data_head">卡片操作记录</div>
            <div class="content_page">
                <!-- 搜索 -->
                <search-form
                    v-model:formState="query"
                    :formList="formList"
                    @submit="getInitList"
                    layout="horizontal"
                    @reset="reset"
                />
                <div class="btn_group">
                    <a-button @click="batchSingleBind([selectId])"
                        >关联用户信息</a-button
                    >
                    <!-- <a-button :disabled="!selectedRowKeys.length" danger ghost
						@click="delectUser(selectedRowKeys)">批量删除</a-button> -->
                </div>
                <ETable
                    row="id"
                    :scroll="{ x: 1200 }"
                    :style="`max-width: ${tableWidth}`"
                    :columns="columns"
                    :data-source="dataSource"
                    :paginations="pagination"
                    @change="handleTableChange"
                    :loading="tableLoading"
                    :row-selection="{
                        selectedRowKeys: selectedRowKeys,
                        onChange: onSelectChange
                    }"
                >
                    <template #bodyCell="{ column, record, index, text }">
                        <template v-if="column.dataIndex == 'index'">
                            {{ index + 1 }}
                        </template>
                        <template v-else-if="column.dataIndex === 'gender'">
                            {{ ["女", "男"][record.gender] || "未知" }}
                        </template>
                        <template v-else-if="column.dataIndex === 'imgPath'">
                            <a-image
                                :width="50"
                                :src="record.imgPath"
                                v-if="record.imgPath"
                            />
                            <span v-else>--</span>
                        </template>
                        <template v-else-if="column.dataIndex == 'operate'">
                            <a-button
                                type="link"
                                class="btn-link-color"
                                @click="synchPersonnel(record)"
                                >同步</a-button
                            >
                            <a-button
                                type="link"
                                danger
                                ghost
                                @click="delectUser([record.id])"
                                >删除</a-button
                            >
                        </template>
                        <template v-else>
                            <Tooltip
                                :maxWidth="column.width - 20"
                                :title="text"
                            ></Tooltip>
                        </template>
                    </template>
                </ETable>
            </div>
        </div>
        <!-- 关联用户信息 -->
        <YSelect
            v-model:visible="selectOpen"
            :tabs="selectTabs"
            :maximum="100"
            :isPickType="[
                'dept',
                'school',
                'campus',
                'academics',
                'grade',
                'classes',
                'person'
            ]"
            @confirm="handerConfirm"
        />
    </div>
</template>

<script setup>
import { message, Modal } from "ant-design-vue"
import { createVNode, ref } from "vue"
import { ExclamationCircleFilled } from "@ant-design/icons-vue"

import useStore from "@/store"
import DeviceList from "./components/deviceList.vue"

const deviceListRef = ref(null)
const { system } = useStore()
const selectId = ref(null)
const tableLoading = ref(false)
const deviceList = ref([])
const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})
const columns = ref([
    { title: "序号", dataIndex: "index", width: 80 },
    { title: "ID", dataIndex: "id", key: "id", width: 180 },
    { title: "姓名", dataIndex: "name", key: "name", width: 150 },
    { title: "性别", dataIndex: "gender", key: "gender", width: 80 },
    { title: "所在组织", dataIndex: "deptName", key: "deptName", width: 180 },
    { title: "IC卡号", dataIndex: "cardNo", key: "cardNo", width: 100 },
    { title: "人脸", dataIndex: "imgPath", key: "imgPath", width: 100 },
    {
        title: "人脸同步状态",
        dataIndex: "faceStatus",
        key: "faceStatus",
        width: 120
    },
    {
        title: "卡号同步状态",
        dataIndex: "cardNoStatus",
        key: "cardNoStatus",
        width: 120
    },
    { title: "操作人员", dataIndex: "createBy", key: "createBy", width: 100 },
    { title: "同步时间", dataIndex: "syncTime", key: "syncTime", width: 160 },
    { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
])

const formList = ref([
    {
        type: "input",
        value: "name",
        label: "姓名"
    }
])

const tableWidth = computed(() => {
    return system.collapsed === "open"
        ? "calc(100vw - 526px)"
        : "calc(100vw - 400px)"
})

const selectOpen = ref(false)
const selectTabs = [
    {
        tab: "教职工",
        key: 2,
        businessType: 20,
        code: null,
        single: false, // 单选多选
        checked: true
    },
    {
        tab: "学生",
        key: 1,
        businessType: 10,
        code: null,
        single: false, // 单选多选
        checked: true
    }
]

const deviceIds = ref([])
const selectedRowKeys = ref([])

function batchSingleBind(data) {
    deviceIds.value = data
    selectOpen.value = true
}

const onSelectChange = (key) => {
    selectedRowKeys.value = key
}

const handerConfirm = (data) => {
    http.post("/unicard/mgmt/device-sync/bind", {
        personQueryList: data,
        deviceIds: deviceIds.value
    }).then((res) => {
        message.success(res.message)
        reset()
        deviceIds.value = []
        deviceListRef.value?.resetSelect()
    })
}

function synchronize(data) {
    Modal.confirm({
        title: "提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "确定同步列表中所有人员的信息？",
        okText: "确 认",
        cancelText: "取 消",
        onOk() {
            http.post("/unicard/mgmt/device-sync/sync-all", {
                deviceIds: data
            }).then((res) => {
                message.success(res.message)
                deviceListRef.value?.resetSelect()
                reset()
            })
        },
        onCancel() {
            message.info("已取消！")
        }
    })
}
// 删除
function delectUser(data) {
    Modal.confirm({
        title: "提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "确定删除吗？",
        okText: "确 认",
        cancelText: "取 消",
        onOk() {
            http.post("/unicard/mgmt/device-sync/delete", { ids: data }).then(
                (res) => {
                    message.success(res.message)
                    reset()
                    selectedRowKeys.value = []
                }
            )
        },
        onCancel() {
            message.info("已取消！")
        }
    })
}

function synchPersonnel(record) {
    Modal.confirm({
        title: "提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "确定同步选择的人员的信息？",
        okText: "确 认",
        cancelText: "取 消",
        onOk() {
            http.post("/unicard/mgmt/device-sync/sync", { id: record.id }).then(
                (res) => {
                    message.success(res.message)
                    reset()
                }
            )
        },
        onCancel() {
            message.info("已取消！")
        }
    })
}

function getList() {
    tableLoading.value = true
    http.post("/unicard/mgmt/device-sync/page", {
        ...pagination.value,
        ...query.value,
        deviceId: selectId.value
    })
        .then((res) => {
            dataSource.value = res.data?.list
            pagination.value.pageNo = res.data?.pageNo
            pagination.value.pageSize = res.data?.pageSize
            pagination.value.total = res.data?.total
        })
        .finally(() => {
            tableLoading.value = false
        })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

function selectReset(id) {
    if (id) {
        selectId.value = id
        reset()
    } else {
        pagination.value.pageNo = 1
        pagination.value.pageSize = 10
        query.value = {}
        dataSource.value = []
    }
}
</script>

<style lang="less" scoped>
.data_sync {
    width: 100%;
    max-width: 100%;
    min-height: calc(100vh - 120px);
    display: flex;

    .left_device {
        min-width: 252px;
        width: 252px;
        min-height: 100%;
        border-right: 1px solid #d8d8d8;
    }

    .user_content {
        flex: 1;

        .data_head {
            width: 100%;
            padding: 16px 20px;
            border-bottom: 1px solid #d8d8d8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 500;
            font-size: 18px;
            color: var(--text-color);
            line-height: 25px;
        }

        .content_page {
            padding: 20px;
            width: 100%;

            .btn_group {
                display: flex;
                justify-content: flex-end;
                margin: 20px 0;
            }
        }
    }
}

.y-modal-container {
    overflow-y: auto;
    max-height: 606px;
    padding: 20px;
}
</style>
