<template>
    <div class="home">
        <div class="welcome-container">
            <div class="welcome-header">
                <h1>欢迎使用一卡通管理系统</h1>
                <p>系统已成功加载，请从左侧菜单选择功能模块</p>
            </div>

            <div class="quick-actions">
                <h3>快速操作</h3>
                <div class="action-cards">
                    <a-card
                        v-for="action in quickActions"
                        :key="action.key"
                        class="action-card"
                        hoverable
                        @click="handleQuickAction(action)"
                    >
                        <template #cover>
                            <div class="card-icon">
                                <component :is="action.icon" />
                            </div>
                        </template>
                        <a-card-meta
                            :title="action.title"
                            :description="action.description"
                        />
                    </a-card>
                </div>
            </div>

            <div class="system-info">
                <h3>系统信息</h3>
                <a-descriptions bordered :column="2">
                    <a-descriptions-item label="当前用户">{{ userInfo.name || '管理员' }}</a-descriptions-item>
                    <a-descriptions-item label="登录时间">{{ loginTime }}</a-descriptions-item>
                    <a-descriptions-item label="系统版本">v1.0.0</a-descriptions-item>
                    <a-descriptions-item label="环境">{{ environment }}</a-descriptions-item>
                </a-descriptions>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
    UserOutlined,
    CreditCardOutlined,
    ShopOutlined,
    SettingOutlined
} from '@ant-design/icons-vue'
import useStore from '@/store'

const router = useRouter()
const { user } = useStore()

// 响应式数据
const loginTime = ref('')
const environment = ref(import.meta.env.MODE)

// 计算属性
const userInfo = computed(() => user.getUser)

// 快速操作配置
const quickActions = ref([
    {
        key: 'merchant',
        title: '商户管理',
        description: '管理商户信息和配置',
        icon: ShopOutlined,
        path: '/merchantManage'
    },
    {
        key: 'card',
        title: '卡片管理',
        description: '管理一卡通卡片',
        icon: CreditCardOutlined,
        path: '/payManage/cardManage'
    },
    {
        key: 'user',
        title: '用户管理',
        description: '管理系统用户',
        icon: UserOutlined,
        path: '/userManage'
    },
    {
        key: 'setting',
        title: '系统设置',
        description: '系统配置和参数',
        icon: SettingOutlined,
        path: '/systemSetting'
    }
])

// 方法
const handleQuickAction = (action: any) => {
    // 检查路由是否存在
    const routes = router.getRoutes()
    const targetRoute = routes.find(r => r.path === action.path)

    if (targetRoute) {
        router.push(action.path)
    } else {
        console.log(`路由 ${action.path} 不存在，请检查权限配置`)
    }
}

// 生命周期
onMounted(() => {
    // 设置登录时间
    loginTime.value = new Date().toLocaleString()

    // 打印调试信息
    console.log('首页已加载')
    console.log('用户信息:', userInfo.value)
    console.log('可用路由:', router.getRoutes().map(r => ({ name: r.name, path: r.path })))
})
</script>

<style scoped>
.home {
    height: calc(100vh - 120px);
    padding: 24px;
    background: #f5f5f5;
}

.welcome-container {
    max-width: 1200px;
    margin: 0 auto;
}

.welcome-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.welcome-header h1 {
    color: #1890ff;
    margin-bottom: 16px;
    font-size: 32px;
}

.welcome-header p {
    color: #666;
    font-size: 16px;
}

.quick-actions {
    margin-bottom: 40px;
}

.quick-actions h3 {
    margin-bottom: 20px;
    color: #333;
}

.action-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.action-card {
    cursor: pointer;
    transition: all 0.3s;
}

.action-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.card-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;
    font-size: 32px;
    color: #1890ff;
    background: #f0f8ff;
}

.system-info {
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.system-info h3 {
    margin-bottom: 20px;
    color: #333;
}
</style>
