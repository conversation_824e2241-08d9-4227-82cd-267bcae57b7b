<template>
    <div class="page_content">
        <!-- 头部 -->
        <div class="card_head">
            <span class="title">设备结算报表</span>
        </div>
        <div class="content_page">
            <!-- 搜索组件区域 -->
            <search-form
                style="margin-bottom: 20px"
                v-model:formState="query"
                :formList="formList"
                @submit="getInitList"
                layout="horizontal"
                @reset="reset"
            />

            <!-- 按钮区域 -->
            <div class="btn_group">
                <a-button>导出</a-button>
            </div>
            <div class="table_box">
                <!-- 表格 -->
                <ETable
                    :scroll="{ x: 1500 }"
                    :columns="columns"
                    :loading="tableLoading"
                    :data-source="dataSource"
                    :paginations="pagination"
                    @change="handleTableChange"
                >
                </ETable>
            </div>
        </div>
    </div>
</template>

<script setup>
const commodityDrawerRef = ref(null)
const query = ref({})
const tableLoading = ref(false)
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})
const columns = ref([
    { title: "数据日期", dataIndex: "dataTime", key: "dataTime" },
    {
        title: "设备IMEI",
        dataIndex: "educationalSystem",
        key: "educationalSystem"
    },
    { title: "设备名称", dataIndex: "deviceName", key: "deviceName" },
    {
        title: "支付总金额",
        dataIndex: "incomeTotalAmount",
        key: "incomeTotalAmount"
    },
    { title: "支付总笔数", dataIndex: "incomeTotalNum", key: "incomeTotalNum" },
    { title: "刷卡支付笔数", dataIndex: "incomeCardNum", key: "incomeCardNum" },
    {
        title: "刷卡总金额",
        dataIndex: "incomeCardAmount",
        key: "incomeCardAmount"
    },
    { title: "刷脸支付笔数", dataIndex: "incomeFaceNum", key: "incomeFaceNum" },
    {
        title: "刷脸总金额",
        dataIndex: "incomeFaceAmount",
        key: "incomeFaceAmount"
    },
    { title: "现金支付笔数", dataIndex: "incomeCashNum", key: "incomeCashNum" },
    {
        title: "现金支付总金额",
        dataIndex: "incomeCashAmount",
        key: "incomeCashAmount"
    }
])

const formList = ref([
    {
        type: "input",
        value: "deviceName",
        label: "设备名称"
    },
    {
        type: "input",
        value: "deviceImei",
        label: "设备编号"
    },
    {
        type: "rangePicker",
        value: ["startTime", "endTime"],
        label: "数据日期",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

function getList() {
    tableLoading.value = true
    http.post("/unicard/mgmt/settlement-report/device-report", {
        ...pagination.value,
        ...query.value
    })
        .then((res) => {
            dataSource.value = res.data?.list
            pagination.value.pageNo = res.data?.pageNo
            pagination.value.pageSize = res.data?.pageSize
            pagination.value.total = res.data?.total
        })
        .finally(() => {
            tableLoading.value = false
        })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.page_content {
    background: #ffffff;
    min-height: calc(100vh - 120px);

    .card_head {
        width: 100%;
        padding: 16px 20px;
        border-bottom: 1px solid #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title {
            font-weight: 500;
            font-size: 18px;
            color: var(--text-color);
            line-height: 25px;
        }
    }

    .content_page {
        padding: 20px;
        width: 100%;

        .btn_group {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 16px;
        }

        .table_box {
            width: 100%;
        }
    }
}
</style>
