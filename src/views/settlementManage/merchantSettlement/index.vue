<template>
    <div class="page_content">
        <!-- 头部 -->
        <div class="card_head">
            <span class="title">商户结算报表</span>
        </div>
        <div class="content_page">
            <!-- 搜索组件区域 -->
            <search-form
                style="margin-bottom: 20px"
                v-model:formState="query"
                :formList="formList"
                @submit="getInitList"
                layout="horizontal"
                @reset="reset"
            />

            <!-- 按钮区域 -->
            <div class="btn_group">
                <a-button>导出</a-button>
            </div>
            <!-- 表格 -->
            <ETable
                :scroll="{ x: 1500 }"
                :bordered="true"
                :columns="columns"
                :data-source="dataSource"
                :paginations="pagination"
                @change="handleTableChange"
                :loading="tableLoading"
            >
            </ETable>
        </div>
    </div>
</template>

<script setup>
const query = ref({})
const dataSource = ref([])
const tableLoading = ref(false)
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const formList = ref([
    {
        type: "input",
        value: "merchantName",
        label: "商户名称"
    },
    {
        type: "rangePicker",
        value: ["startTime", "endTime"],
        label: "数据日期",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const columns = ref([
    { title: "数据日期", dataIndex: "dataTime", key: "dataTime" },
    { title: "商户名称", dataIndex: "merchantName", key: "merchantName" },
    {
        title: "收款总金额",
        dataIndex: "incomeTotal",
        key: "incomeTotal",
        children: [
            {
                title: "总笔数",
                dataIndex: "incomeTotalNum",
                key: "incomeTotalNum"
            },
            {
                title: "总金额",
                dataIndex: "incomeTotalAmount",
                key: "incomeTotalAmount"
            }
        ]
    },
    {
        title: "刷脸支付",
        dataIndex: "incomeFace",
        key: "incomeFace",
        children: [
            {
                title: "笔数",
                dataIndex: "incomeFaceNum",
                key: "incomeFaceNum"
            },
            {
                title: "金额",
                dataIndex: "incomeFaceAmount",
                key: "incomeFaceAmount"
            }
        ]
    },
    {
        title: "刷卡支付",
        dataIndex: "incomeCard",
        key: "incomeCard",
        children: [
            {
                title: "笔数",
                dataIndex: "incomeCardNum",
                key: "incomeCardNum"
            },
            {
                title: "金额",
                dataIndex: "incomeCardAmount",
                key: "incomeCardAmount"
            }
        ]
    },
    {
        title: "微信支付",
        dataIndex: "incomeWx",
        key: "incomeWx",
        children: [
            {
                title: "笔数",
                dataIndex: "incomeWxNum",
                key: "incomeWxNum"
            },
            {
                title: "金额",
                dataIndex: "incomeWxAmount",
                key: "incomeWxAmount"
            }
        ]
    },
    {
        title: "现金",
        dataIndex: "incomeCash",
        key: "incomeCash",
        children: [
            {
                title: "笔数",
                dataIndex: "incomeCashNum",
                key: "incomeCashNum"
            },
            {
                title: "金额",
                dataIndex: "incomeCashAmount",
                key: "incomeCashAmount"
            }
        ]
    },
    {
        title: "退款订单",
        dataIndex: "refund",
        key: "refund",
        children: [
            {
                title: "总计",
                dataIndex: "refundTotal",
                key: "refundTotal",
                children: [
                    {
                        title: "笔数",
                        dataIndex: "refundCashNum",
                        key: "refundCashNum"
                    },
                    {
                        title: "金额",
                        dataIndex: "refundCashAmount",
                        key: "refundCashAmount"
                    }
                ]
            },

            {
                title: "一卡通",
                dataIndex: "refundUnicard",
                key: "refundUnicard",
                children: [
                    {
                        title: "笔数",
                        dataIndex: "refundUnicardNum",
                        key: "refundUnicardNum"
                    },
                    {
                        title: "金额",
                        dataIndex: "refundUnicardAmount",
                        key: "refundUnicardAmount"
                    }
                ]
            },
            {
                title: "现金",
                dataIndex: "refundCash",
                key: "refundCash",
                children: [
                    {
                        title: "笔数",
                        dataIndex: "refundCashNum",
                        key: "refundCashNum"
                    },
                    {
                        title: "金额",
                        dataIndex: "refundCashAmount",
                        key: "refundCashAmount"
                    }
                ]
            },
            {
                title: "微信支付码",
                dataIndex: "arefundWx",
                key: "refundWx",
                children: [
                    {
                        title: "笔数",
                        dataIndex: "refundWxNum",
                        key: "refundWxNum"
                    },
                    {
                        title: "金额",
                        dataIndex: "refundWxAmount",
                        key: "refundWxAmount"
                    }
                ]
            },
            {
                title: "其他",
                dataIndex: "refundOther",
                key: "refundOther",
                children: [
                    {
                        title: "笔数",
                        dataIndex: "refundOtherNum",
                        key: "refundOtherNum"
                    },
                    {
                        title: "金额",
                        dataIndex: "refundOtherAmount",
                        key: "refundOtherAmount"
                    }
                ]
            }
        ]
    }
])

function getList() {
    tableLoading.value = true
    http.post("/unicard/mgmt/settlement-report/merchant-report", {
        ...pagination.value,
        ...query.value
    })
        .then((res) => {
            dataSource.value = res.data?.list
            pagination.value.pageNo = res.data?.pageNo
            pagination.value.pageSize = res.data?.pageSize
            pagination.value.total = res.data?.total
        })
        .finally(() => {
            tableLoading.value = false
        })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.page_content {
    background: #ffffff;

    .card_head {
        width: 100%;
        padding: 16px 20px;
        border-bottom: 1px solid #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title {
            font-weight: 500;
            font-size: 18px;
            color: var(--text-color);
            line-height: 25px;
        }
    }

    .content_page {
        padding: 20px;
    }

    .btn_group {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-bottom: 16px;
    }
}
</style>
