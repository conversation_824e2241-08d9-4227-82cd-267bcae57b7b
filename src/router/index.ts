/*
 * @Descripttion: 路由配置
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2023-03-07 09:40:26
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-06-18 22:57:07
 */
import { createRouter, createWebHashHistory, RouteRecordRaw } from "vue-router"
import NProgress from "nprogress"
import "nprogress/nprogress.css"
import systemConfig from "../../config/index"
import { globalLogin } from "@/utils/index"
import { removeAllPendingRequest } from "@/utils/http"
import { getSystemRouter } from "@/api"
import useStore from "@/store"
import { constantRoutes, asyncRouterMap, rootRouter } from "./router.config"

// 开发环境下引入路由测试工具
// if (import.meta.env.MODE === 'development') {
//     import("@/utils/router-test")
// }
const { title } = systemConfig

NProgress.configure({
    // eslint-disable-next-line quotes
    template:
        '<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>',
    showSpinner: false // 转轮
})

const router = createRouter({
    history: createWebHashHistory(),
    routes: constantRoutes,
    scrollBehavior() {
        return { top: 0, left: 0 }
    }
})

const whiteList: string[] = ["user", "login", "register", "NotFound"]

const generatorRouterCompMap = (routes: Array<RouteRecordRaw>) => {
    const fileterRoutes = routes.filter(
        (item) => !["any"].includes(item.name as string)
    )
    const obj: Record<string, any> = {}
    const getComp = (arr: Array<RouteRecordRaw>) => {
        arr.forEach((item) => {
            if (item.children?.length) {
                getComp(item.children)
            }
            obj[item.name as string] = item
        })
    }
    getComp(fileterRoutes)
    return obj
}
// 前端路由表
const constantRouterComponents = generatorRouterCompMap(
    asyncRouterMap[0]?.children as RouteRecordRaw[]
)

const generator = (routerMap: any[], parent?: object | null) => {
    return routerMap.map((item) => {
        const {
            name,
            path,
            icon,
            component,
            redirect,
            isFrame,
            link,
            target,
            btnList = [], // 添加默认值防止undefined
            children
        } = item

        // 调试信息：打印后端返回的路由项
        console.log('后端路由项:', item)

        // 处理权限按钮列表
        const permission = btnList.map(
            (i: { name: string; perms: string; id: number }) => {
                return {
                    name: i.name,
                    perms: i.perms
                }
            }
        )

        // 查找组件的逻辑优化
        const componentKey = item.component || item.key || item.name
        const foundComponent = constantRouterComponents[componentKey]

        // 调试信息：打印组件查找结果
        console.log(`查找组件 ${componentKey}:`, foundComponent)

        const currentRouter: any = {
            // 路径处理
            path: path || `${(parent && (parent as any).path) || ""}/${item.path || item.name}`,
            // 路由名称，使用component字段作为name
            name: component || item.name,
            // 组件处理 - 优先使用预定义组件，否则尝试动态导入
            component: foundComponent?.component || (() => {
                // 动态导入组件的逻辑
                const componentPath = item.component || item.name
                console.log(`动态导入组件: @/views/${componentPath}/index.vue`)
                return import(`@/views/${componentPath}/index.vue`).catch(err => {
                    console.error(`无法导入组件 @/views/${componentPath}/index.vue:`, err)
                    // 返回一个默认的错误组件
                    return import("@/views/error/404.vue")
                })
            }),
            // meta信息
            meta: {
                title: name,
                icon: icon || undefined,
                hiddenHeaderContent: false,
                target: target || "_self",
                permission,
                hidden: foundComponent?.meta?.hidden || false
            },
            hideInMenu: foundComponent?.hideInMenu || false
        }

        // 处理重定向
        if (foundComponent?.redirect) {
            currentRouter.redirect = foundComponent.redirect
        }
        if (redirect) {
            currentRouter.redirect = redirect
        }

        // 处理iframe页面
        if (isFrame) {
            currentRouter.meta.isFrame = true
            currentRouter.meta.href = link
            currentRouter.meta.target = "_blank"
            currentRouter.component = () =>
                import("@/components/webOpen/index.vue")
        }

        // 递归处理子路由
        if (children && children.length > 0) {
            currentRouter.children = generator(children, currentRouter)
        }

        // 组件检查和错误提示
        if (!currentRouter.component) {
            console.error(`${path || item.name} 未找到路由组件，请检查路由配置`)
            console.error('可用的组件映射:', Object.keys(constantRouterComponents))
        }

        return currentRouter
    })
}

async function initRouters(user: any) {
    // 前端路由
    // const permission = asyncRouterMap[0]
    // user.setPermission(asyncRouterMap)
    // router.addRoute(permission)

    // 后端路由
    const { data } = await getSystemRouter()
    console.log("后端返回的路由数据:", data)

    // 确保data是数组
    const routeData = Array.isArray(data) ? data : []
    const generatedRoutes = generator(routeData)
    console.log("生成的路由:", generatedRoutes)

    // 找到第一个可用的路由作为默认重定向
    const findFirstRoute = (routes: any[]): string | null => {
        for (const route of routes) {
            if (route.path && route.component && !route.meta?.hidden) {
                return route.path
            }
            if (route.children && route.children.length > 0) {
                const childRoute = findFirstRoute(route.children)
                if (childRoute) return childRoute
            }
        }
        return null
    }

    const firstRoutePath = findFirstRoute(generatedRoutes)
    console.log("找到的第一个可用路由:", firstRoutePath)

    // 设置重定向：优先使用第一个动态路由，否则使用默认首页
    const redirectPath = firstRoutePath || "/home"
    rootRouter.redirect = redirectPath
    console.log("设置根路由重定向到:", redirectPath)

    // 确保rootRouter.children是数组
    const existingChildren = Array.isArray(rootRouter.children) ? rootRouter.children : []
    rootRouter.children = [...generatedRoutes, ...existingChildren]

    user.setPermission([rootRouter])
    router.addRoute(rootRouter)
}

router.beforeEach(async (to, _from, next) => {
    if (to.meta.title) window.document.title = `${title} - ${to.meta.title}`
    NProgress.start()
    // 跳转页面取消上一个页面所有的请求
    removeAllPendingRequest()
    if (to.query?.token) {
        localStorage.setItem("token", to.query.token as string)
    }
    if (localStorage.getItem("token")) {
        if (to.path === "/user/login") {
            next({ path: "/" })
            NProgress.done()
        } else {
            const { user } = useStore()
            if (user.getPermission.length) {
                // 如果访问根路径且没有重定向，尝试重定向到第一个可用路由
                if (to.path === "/" && !to.redirectedFrom) {
                    const routes = router.getRoutes()
                    const indexRoute = routes.find(r => r.name === "index")
                    if (indexRoute?.redirect) {
                        next({ path: indexRoute.redirect as string })
                        return
                    }
                    // 如果没有设置重定向，查找第一个可用的业务路由
                    const businessRoute = routes.find(r =>
                        r.path !== "/" &&
                        r.path !== "/user/login" &&
                        !r.path.includes("error") &&
                        !r.path.includes("*") &&
                        (r.components?.default || r.component)
                    )
                    if (businessRoute) {
                        next({ path: businessRoute.path })
                        return
                    }
                }
                next()
            } else {
                try {
                    await initRouters(user)
                    // 初始化路由后，如果访问根路径，重定向到第一个可用路由
                    if (to.path === "/" || to.path === "/index") {
                        const routes = router.getRoutes()
                        const indexRoute = routes.find(r => r.name === "index")
                        if (indexRoute?.redirect) {
                            next({ path: indexRoute.redirect as string, replace: true })
                        } else {
                            next({ ...to, replace: true })
                        }
                    } else {
                        next({ ...to, replace: true })
                    }
                } catch (error) {
                    console.error("路由初始化失败:", error)
                    localStorage.removeItem("token")
                    next({ name: "login" })
                    NProgress.done()
                }
            }
        }
    } else {
        if (whiteList.includes(to.name as string)) {
            next()
        } else {
            next({ path: "/user/login", query: { redirect: to.fullPath } })
            NProgress.done()
        }
    }
})

router.afterEach(() => {
    NProgress.done()
    globalLogin("hide")
})

export default router
