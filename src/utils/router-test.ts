/**
 * 路由系统测试工具
 * 用于测试和验证路由组件生成功能
 */

// 模拟后端返回的路由数据
export const mockRouterData = [
    {
        name: "商户管理",
        path: "/merchantManage",
        component: "merchantManage",
        icon: "home-outlined",
        btnList: [
            {
                name: "查看",
                perms: "merchant:view",
                id: 1
            },
            {
                name: "编辑", 
                perms: "merchant:edit",
                id: 2
            }
        ],
        children: [
            {
                name: "微信收款配置",
                path: "/merchantManage/moneyConfig",
                component: "moneyConfig",
                icon: "setting-outlined",
                btnList: [
                    {
                        name: "配置",
                        perms: "merchant:config",
                        id: 3
                    }
                ]
            }
        ]
    },
    {
        name: "支付管理",
        path: "/payManage", 
        component: "payManage",
        icon: "pay-circle-outlined",
        redirect: "/payManage/cardManage",
        btnList: [
            {
                name: "查看",
                perms: "pay:view", 
                id: 4
            }
        ],
        children: [
            {
                name: "卡片管理",
                path: "/payManage/cardManage",
                component: "cardManage",
                icon: "credit-card-outlined",
                btnList: [
                    {
                        name: "管理",
                        perms: "card:manage",
                        id: 5
                    }
                ]
            }
        ]
    }
]

// 测试组件查找功能
export function testComponentLookup() {
    console.log('=== 路由组件查找测试 ===')
    
    mockRouterData.forEach(route => {
        console.log(`测试路由: ${route.name}`)
        console.log(`- 路径: ${route.path}`)
        console.log(`- 组件: ${route.component}`)
        console.log(`- 权限数量: ${route.btnList?.length || 0}`)
        
        if (route.children) {
            console.log(`- 子路由数量: ${route.children.length}`)
            route.children.forEach(child => {
                console.log(`  - 子路由: ${child.name} (${child.component})`)
            })
        }
        console.log('---')
    })
}

// 验证路由数据格式
export function validateRouterData(data: any[]): boolean {
    console.log('=== 路由数据格式验证 ===')
    
    if (!Array.isArray(data)) {
        console.error('❌ 路由数据不是数组格式')
        return false
    }
    
    let isValid = true
    
    data.forEach((item, index) => {
        console.log(`验证路由项 ${index + 1}: ${item.name || '未命名'}`)
        
        // 检查必需字段
        if (!item.name) {
            console.error(`❌ 缺少 name 字段`)
            isValid = false
        }
        
        if (!item.path) {
            console.error(`❌ 缺少 path 字段`)
            isValid = false
        }
        
        if (!item.component) {
            console.warn(`⚠️  缺少 component 字段，将使用 name 作为组件名`)
        }
        
        // 检查 btnList 格式
        if (item.btnList && !Array.isArray(item.btnList)) {
            console.error(`❌ btnList 不是数组格式`)
            isValid = false
        }
        
        // 递归检查子路由
        if (item.children && Array.isArray(item.children)) {
            const childValid = validateRouterData(item.children)
            isValid = isValid && childValid
        }
        
        if (isValid) {
            console.log(`✅ 路由项验证通过`)
        }
        console.log('---')
    })
    
    return isValid
}

// 生成组件路径映射
export function generateComponentPathMap(data: any[]): Record<string, string> {
    const pathMap: Record<string, string> = {}
    
    function traverse(routes: any[], prefix = '') {
        routes.forEach(route => {
            const componentName = route.component || route.name
            const fullPath = `${prefix}${route.path}`
            
            pathMap[componentName] = fullPath
            
            if (route.children && Array.isArray(route.children)) {
                traverse(route.children, fullPath)
            }
        })
    }
    
    traverse(data)
    return pathMap
}

// 检查组件文件是否存在（仅用于开发环境）
export async function checkComponentFiles(componentNames: string[]): Promise<void> {
    console.log('=== 组件文件存在性检查 ===')
    
    for (const componentName of componentNames) {
        try {
            // 尝试动态导入组件
            await import(`@/views/${componentName}/index.vue`)
            console.log(`✅ 组件文件存在: @/views/${componentName}/index.vue`)
        } catch (error) {
            console.error(`❌ 组件文件不存在: @/views/${componentName}/index.vue`)
            console.error(`   建议创建文件或检查组件名称是否正确`)
        }
    }
}

// 提取所有组件名称
export function extractComponentNames(data: any[]): string[] {
    const componentNames: string[] = []
    
    function traverse(routes: any[]) {
        routes.forEach(route => {
            const componentName = route.component || route.name
            if (componentName && !componentNames.includes(componentName)) {
                componentNames.push(componentName)
            }
            
            if (route.children && Array.isArray(route.children)) {
                traverse(route.children)
            }
        })
    }
    
    traverse(data)
    return componentNames
}

// 完整的路由测试套件
export async function runRouterTests(routerData?: any[]): Promise<void> {
    const testData = routerData || mockRouterData
    
    console.log('🚀 开始路由系统测试...')
    console.log('')
    
    // 1. 数据格式验证
    const isValid = validateRouterData(testData)
    if (!isValid) {
        console.error('❌ 路由数据格式验证失败，请修复后重试')
        return
    }
    
    // 2. 组件查找测试
    testComponentLookup()
    
    // 3. 生成路径映射
    const pathMap = generateComponentPathMap(testData)
    console.log('=== 组件路径映射 ===')
    console.table(pathMap)
    
    // 4. 检查组件文件（开发环境）
    if (import.meta.env.MODE === 'development') {
        const componentNames = extractComponentNames(testData)
        await checkComponentFiles(componentNames)
    }
    
    console.log('')
    console.log('✅ 路由系统测试完成')
}

// 在控制台中使用的快捷方法
if (import.meta.env.MODE === 'development') {
    // 将测试函数挂载到 window 对象，方便在控制台调用
    (window as any).routerTest = {
        runTests: runRouterTests,
        validateData: validateRouterData,
        checkComponents: checkComponentFiles,
        mockData: mockRouterData
    }
    
    console.log('🔧 路由测试工具已加载，可在控制台使用:')
    console.log('- routerTest.runTests() - 运行完整测试')
    console.log('- routerTest.validateData(data) - 验证路由数据')
    console.log('- routerTest.checkComponents(names) - 检查组件文件')
    console.log('- routerTest.mockData - 查看模拟数据')
}
